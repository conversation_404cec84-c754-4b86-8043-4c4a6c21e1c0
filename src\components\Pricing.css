.pricing-page {
  font-family: 'Kanit', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
}

/* Header Styles */
.header {
  padding: 1rem 2rem;
  position: relative;
  z-index: 10;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-left {
  display: flex;
  align-items: center;
}

.logo {
  height: 32px;
  width: auto;
}

.nav-center {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 400;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: white;
}

.nav-right {
  display: flex;
  align-items: center;
}

.dashboard-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dashboard-btn:hover {
  background: #4338ca;
}

/* Main Content */
.main {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.pricing-header {
  margin-bottom: 3rem;
  text-align: center;
}

.pricing-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.pricing-subtitle {
  font-size: 1.1rem;
  color: #a0a0a0;
  margin: 0;
}

.carousel-container {
  position: relative;
  width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
}

.pricing-carousel {
  width: 100%;
  overflow: visible;
}

.pricing-plans {
  display: flex;
  gap: 28px;
  padding: 1rem 0;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 100%;
}

.plan-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 28px;
  transition: all 0.3s ease;
  width: 355px;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;
  height: 779px;
  min-width: 300px;
}

.plan-card.featured {
  border-color: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
}

.plan-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.plan-header {
  margin-bottom: 1.5rem;
}

.plan-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.plan-description {
  color: #a0a0a0;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.plan-pricing {
  margin-bottom: 1.5rem;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.period {
  display: block;
  color: #a0a0a0;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.plan-button {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  font-family: 'Kanit', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 1.5rem;
}

.plan-button.primary {
  background: #4f46e5;
  color: white;
}

.plan-button.primary:hover {
  background: #4338ca;
}

.plan-button.secondary {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.plan-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 0;
  flex: 1;
}

.feature {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  height: 34px;
  margin: 0;
  padding: 0;
  line-height: 1;
}

.feature-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
  color: #e5e5e5;
  font-size: 12px;
  line-height: 1;
  height: 18px;
  display: flex;
  align-items: center;
}

.feature-value {
  color: #a0a0a0;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
  font-size: 12px;
  line-height: 1;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.feature.disabled {
  color: #46464D;
}

.feature.disabled .feature-text {
  color: #46464D;
}

.feature.disabled .feature-value {
  color: #46464D;
}

.feature.disabled .feature-icon:not([src*="x.svg"]) {
  filter: brightness(0.4);
}

/* Responsive Design */
@media (max-width: 1600px) {
  .pricing-plans {
    justify-content: flex-start;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    flex-wrap: nowrap;
    padding: 1rem 0;
  }

  .pricing-plans::-webkit-scrollbar {
    display: none;
  }

  .carousel-container {
    overflow-x: auto;
  }
}

@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-center {
    order: 2;
  }

  .pricing-title {
    font-size: 2rem;
  }

  .pricing-subtitle {
    font-size: 1rem;
  }

  .plan-card {
    width: 300px;
    min-width: 280px;
    padding: 1.25rem;
    height: auto;
    min-height: 700px;
  }

  .main {
    padding: 1rem;
  }

  .carousel-container {
    padding: 0 1rem;
  }

  .pricing-plans {
    gap: 20px;
  }
}
